package com.chic.dea.apis.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 提数任务详情视图对象VO
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
public class TaskDetailVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * OA ID
     */
    private String oaId;

    /**
     * 任务标题
     */
    private String title;

    /**
     * 申请人
     */
    private String applicant;

    /**
     * 申请人邮箱
     */
    private String applicantEmail;

    /**
     * 申请人手机
     */
    private String applicantPhone;

    /**
     * 提取状态
     */
    private String extractionStatus;

    /**
     * 提取状态名称
     */
    private String extractionStatusName;

    /**
     * 提取脚本
     */
    private String extractionScript;

    /**
     * 数据源ID
     */
    private Long dataSourceId;

    /**
     * 数据源名称
     */
    private String dataSourceName;

    /**
     * 数据源类型
     */
    private String dataSourceType;

    /**
     * 合规OA文件URL
     */
    private String compliancePdfUrl;

    /**
     * 样例数据URL
     */
    private String sampleDataUrl;

    /**
     * 结果文件URL
     */
    private String resultFileUrl;

    /**
     * 文件密码
     */
    private String filePassword;

    /**
     * 总记录数
     */
    private Long totalRecords;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 文件大小可读格式
     */
    private String fileSizeReadable;

    /**
     * 执行开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime executionStartTime;

    /**
     * 执行结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime executionEndTime;

    /**
     * 执行时长（毫秒）
     */
    private Long executionDuration;

    /**
     * 执行时长可读格式
     */
    private String executionDurationReadable;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 修改人名称
     */
    private String updateByName;

    /**
     * 是否可编辑
     */
    private Boolean editable;

    /**
     * 是否可执行
     */
    private Boolean executable;

    /**
     * 是否可归档
     */
    private Boolean archivable;
}
