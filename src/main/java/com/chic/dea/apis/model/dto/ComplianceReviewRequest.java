package com.chic.dea.apis.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 合规审核请求DTO
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
public class ComplianceReviewRequest {

    /**
     * 审核意见：APPROVED-通过，REJECTED-不通过
     */
    @NotBlank(message = "审核意见不能为空")
    private String reviewOpinion;

    /**
     * 审核备注
     */
    private String reviewComment;
}
