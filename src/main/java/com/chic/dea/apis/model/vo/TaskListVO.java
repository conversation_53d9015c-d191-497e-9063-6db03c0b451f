package com.chic.dea.apis.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 提数任务列表视图对象VO
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
public class TaskListVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * OA ID
     */
    private String oaId;

    /**
     * 任务标题
     */
    private String title;

    /**
     * 申请人
     */
    private String applicant;

    /**
     * 申请人邮箱
     */
    private String applicantEmail;

    /**
     * 提取状态
     */
    private String extractionStatus;

    /**
     * 提取状态名称
     */
    private String extractionStatusName;

    /**
     * 数据源ID
     */
    private Long dataSourceId;

    /**
     * 数据源名称
     */
    private String dataSourceName;

    /**
     * 合规审核意见
     */
    private String complianceReviewOpinion;

    /**
     * 合规审核意见名称
     */
    private String complianceReviewOpinionName;

    /**
     * 合规审核人
     */
    private String complianceReviewer;

    /**
     * 合规审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime complianceReviewTime;

    /**
     * 总记录数
     */
    private Long totalRecords;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 执行开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime executionStartTime;

    /**
     * 执行结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime executionEndTime;

    /**
     * 执行时长（毫秒）
     */
    private Long executionDuration;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 是否可编辑
     */
    private Boolean editable;

    /**
     * 是否可执行
     */
    private Boolean executable;

    /**
     * 结果文件URL
     */
    private String resultFileUrl;
}
