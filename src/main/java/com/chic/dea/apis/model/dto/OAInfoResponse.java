package com.chic.dea.apis.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * OA信息响应DTO
 * 
 * <AUTHOR>
 * @since 2024-12-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OAInfoResponse {
    /**
     * OA
     */
    private String bt;

    /**
     * 邮箱
     */
    private String yx;

    /**
     * 手机号码
     */
    private String sjhm;

    /**
     * 标题名称
     */
    private String btqz;

    /**
     * 申请人名称
     */
    private String sqrqz;

    /**
     * 分公司名称
     */
    private String fqgsqz;

    /**
     * 查询是否成功
     */
    private Boolean success;

    /**
     * 错误信息(查询失败时)
     */
    private String errorMessage;

    /**
     * 创建成功响应
     * 
     * @param bt 标题
     * @param yx 邮箱
     * @param sjhm 手机号码
     * @param btqz 标题名称
     * @param sqrqz 申请人名称
     * @param fqgsqz 分公司名称
     * @return 成功响应
     */
    public static OAInfoResponse success(String bt, String yx, String sjhm, String btqz, String sqrqz, String fqgsqz) {
        return OAInfoResponse.builder()

                .bt(bt)
                .yx(yx)
                .sjhm(sjhm)
                .btqz(btqz)
                .sqrqz(sqrqz)
                .fqgsqz(fqgsqz)
                .success(true)
                .build();
    }

    /**
     * 创建失败响应
     * 
     * @param errorMessage 错误信息
     * @return 失败响应
     */
    public static OAInfoResponse fail(String errorMessage) {
        return OAInfoResponse.builder()
                .success(false)
                .errorMessage(errorMessage)
                .build();
    }
}
