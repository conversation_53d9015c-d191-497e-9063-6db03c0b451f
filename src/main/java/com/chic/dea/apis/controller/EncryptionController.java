package com.chic.dea.apis.controller;

import com.chic.commons.base.Result;
import com.chic.commons.exception.ErrorResult;
import com.chic.commons.exception.ErrorResultCode;
import com.chic.dea.apis.model.dto.*;
import com.chic.dea.domain.service.EncryptionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 加密解密API控制器
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@RestController
@RequestMapping("/df/api/encryption")
@RequiredArgsConstructor
public class EncryptionController {

    private final EncryptionService encryptionService;

    /**
     * 单个文本加密
     *
     * @param plainText 加密请求文本
     * @return 加密响应
     */
    @PostMapping("/encrypt/single")
    public Result<String> encryptSingle(@RequestParam String plainText) {
        log.info("接收单个文本加密请求");

        try {
            return Result.success(encryptionService.encryptSingle(plainText));
        } catch (IllegalArgumentException e) {
            log.warn("单个文本加密参数错误: {}", e.getMessage());
            return Result.fail(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), e.getMessage()));
        } catch (Exception e) {
            log.error("单个文本加密失败", e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "加密失败: " + e.getMessage()));
        }
    }

    /**
     * 单个文本解密
     *
     * @param encryptedText 解密请求
     * @return 解密响应
     */
    @PostMapping("/decrypt/single")
    public Result<String> decryptSingle(@RequestParam String encryptedText) {
        log.info("接收单个文本解密请求");

        try {
            return Result.success(encryptionService.decryptSingle(encryptedText));
        } catch (IllegalArgumentException e) {
            log.warn("单个文本解密参数错误: {}", e.getMessage());
            return Result.fail(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), e.getMessage()));
        } catch (Exception e) {
            log.error("单个文本解密失败", e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "解密失败: " + e.getMessage()));
        }
    }



}
