package com.chic.dea.domain.database.entity;

/**
 * 合规审核意见枚举
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
public enum ComplianceReviewOpinion {
    
    /**
     * 审核通过
     */
    APPROVED("通过"),
    
    /**
     * 审核不通过
     */
    REJECTED("不通过");

    private final String description;

    ComplianceReviewOpinion(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
    
    /**
     * 检查是否为通过状态
     * 
     * @return true if approved
     */
    public boolean isApproved() {
        return this == APPROVED;
    }
    
    /**
     * 检查是否为拒绝状态
     * 
     * @return true if rejected
     */
    public boolean isRejected() {
        return this == REJECTED;
    }
}
