package com.chic.dea.domain.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chic.dea.apis.model.dto.*;
import com.chic.dea.domain.database.entity.*;
import com.chic.dea.domain.database.mapper.*;
import com.chic.dea.domain.service.*;
import com.chic.dea.domain.service.SqlValidationService.*;
import com.chic.dea.domain.service.SensitiveFieldService.*;
import com.chic.dea.infrastructure.general.constants.RedisKeyConstants;
import com.chic.dea.infrastructure.redis.DistributedLockHandler;
import com.chic.dea.infrastructure.redis.RedisLock;
import com.chic.dea.infrastructure.sql.SqlBuilder;
import com.chic.dea.infrastructure.sql.SqlBuilderContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 任务创建服务实现类
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskCreationServiceImpl extends ServiceImpl<ExtractionTaskMapper, ExtractionTask> implements TaskCreationService {

    private final ExtractionTaskMapper taskMapper;
    private final TaskExecutionQueueMapper queueMapper;
    private final DataSourceService dataSourceService;
    private final SqlValidationService sqlValidationService;
    private final SensitiveFieldService sensitiveFieldService;
    private final SqlLineageService sqlLineageService;
    private final DataSourceManager dataSourceManager;
    private final DistributedLockHandler distributedLockHandler;



    @Override
    public SqlValidationResult validateSQL(String sql, Long dataSourceId) {
        log.info("校验SQL脚本, dataSourceId: {}", dataSourceId);

        // 获取数据源信息
        DataSource dataSource = dataSourceService.getDataSourceEntityById(dataSourceId);
        String databaseType = dataSource.getType();

        // 使用Apache Calcite进行SQL语法校验
        SqlValidationResult syntaxResult = sqlValidationService.validateSyntax(sql, databaseType);
        if (!syntaxResult.isValid()) {
            log.error("SQL语法校验失败: {}", syntaxResult.getErrorMessage());
        }
        return syntaxResult;
    }

    private List<LineageTableFieldsVO> getSqlLineage(String sql, String databaseType) {
        // 解析SQL获取涉及的表和字段
        String viewSql = sqlValidationService.createViewClause(sql);
        log.info("生成视图语句: {}", viewSql);
        // 调用SQL血缘服务，获取血缘关系
        return sqlLineageService.extractTableFields(viewSql, databaseType.toLowerCase());
    }

    @Override
    public PreviewDataResponse previewData(String sql, Long dataSourceId) {
        log.info("预览数据, dataSourceId: {}", dataSourceId);
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 获取数据源信息
            DataSource dataSource = dataSourceService.getDataSourceEntityById(dataSourceId);
            String databaseType = dataSource.getType();
            
            // 添加LIMIT子句限制预览数据量
            String limitedSql = sqlValidationService.addLimitClause(sql, 100, databaseType);
            
            // 执行查询并获取预览数据
            List<Map<String, Object>> rows = executePreviewQuery(limitedSql, dataSource);
            
            // 提取列名
            List<String> columns = new ArrayList<>();
            if (!rows.isEmpty()) {
                columns.addAll(rows.get(0).keySet());
            }
            
            long executionTime = System.currentTimeMillis() - startTime;
            
            log.info("预览数据成功, 行数: {}, 执行时间: {}ms", rows.size(), executionTime);
            
            return PreviewDataResponse.success(columns, rows, executionTime);
            
        } catch (Exception e) {
            log.error("预览数据失败", e);
            return PreviewDataResponse.fail("数据预览失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Long createDraftTask(TaskCreateRequest request) {
        log.info("创建草稿任务, oaId: {}", request.getOaId());
        
        // 检查OA ID是否已存在
        if (existsByOaId(request.getOaId())) {
            throw new RuntimeException("OA ID已存在: " + request.getOaId());
        }
        
        // 创建任务实体
        ExtractionTask task = new ExtractionTask();
        BeanUtils.copyProperties(request, task);
        task.setExtractionStatus(ExtractionStatus.DRAFT.name());

        
        // 保存任务
        taskMapper.insert(task);
        
        log.info("草稿任务创建成功, taskId: {}", task.getId());
        return task.getId();
    }

    @Override
    @Transactional
    public TaskSubmissionResponse submitTask(TaskSubmissionRequest request) {
        log.info("提交任务到执行队列, oaId: {}", request.getOaId());

        // 1. 防重复提交检查 - 使用Redis分布式锁
        String lockKey = RedisKeyConstants.TASK_SUBMIT_LOCK_PREFIX + request.getOaId();
        String lockValue = distributedLockHandler.getLockValue();
        RedisLock redisLock = new RedisLock(lockKey, lockValue);

        // 尝试获取锁，防止重复提交（等待时间3秒，锁过期时间30秒）
        boolean lockAcquired = distributedLockHandler.tryLock(redisLock, 3000L, 100L, 30L);
        if (!lockAcquired) {
            log.warn("任务正在提交中，请勿重复提交, oaId: {}", request.getOaId());
            throw new RuntimeException("任务正在提交中，请勿重复提交");
        }

        try {
            // 2. 检查OA ID是否已存在
//            if (existsByOaId(request.getOaid())) {
//                log.warn("OA ID已存在，不允许重复提交, oaId: {}", request.getOaid());
//                throw new RuntimeException("OA ID已存在，不允许重复提交: " + request.getOaid());
//            }

            // 3. 获取数据源信息并进行SQL血缘分析
            DataSource dataSource = dataSourceService.getDataSourceEntityById(request.getDataSourceId());
            String databaseType = dataSource.getType();
            List<LineageTableFieldsVO> lineageTableFieldsVOS = this.getSqlLineage(request.getExtractionScript(), databaseType);

            // 4. 敏感字段检查(调用天御平台)
            List<SensitiveFieldCheckResult> sensitiveFieldCheckResults = sensitiveFieldService.checkSensitiveFields(lineageTableFieldsVOS);
            if (!CollectionUtils.isEmpty(sensitiveFieldCheckResults)) {
                log.warn("涉及敏感字段，需要上传合规审批文件");
                request.setRequiresCompliance(true);
            }

            // 5. 创建任务实体
            ExtractionTask task = new ExtractionTask();
            BeanUtils.copyProperties(request, task);

            // 6. 设置任务状态
            if (request.getRequiresCompliance()) {
                task.setExtractionStatus(ExtractionStatus.PENDING_COMPLIANCE_REVIEW.name());
            } else {
                task.setExtractionStatus(ExtractionStatus.PENDING_EXTRACTION.name());
            }

            // 7. 保存任务
            taskMapper.insert(task);

            // 8. 待提数，则加入执行队列
            if (ExtractionStatus.PENDING_EXTRACTION.name().equals(task.getExtractionStatus())) {
                createExecutionQueueItem(task.getId());
            }

            log.info("任务提交成功, taskId: {}, status: {}", task.getId(), task.getExtractionStatus());

            // 9. 返回结果
            if(request.getRequiresCompliance()){
                return TaskSubmissionResponse.requiresCompliance(task.getId(),sensitiveFieldCheckResults);
            }
            return TaskSubmissionResponse.success(task.getId());

        } catch (Exception e) {
            log.error("任务提交失败, oaId: {}", request.getOaId(), e);
            throw e;
        } finally {
            // 10. 释放分布式锁
            boolean released = distributedLockHandler.releaseLock(redisLock);
            if (released) {
                log.debug("任务提交锁释放成功, oaId: {}", request.getOaId());
            } else {
                log.warn("任务提交锁释放失败, oaId: {}", request.getOaId());
            }
        }
    }

    @Override
    public boolean existsByOaId(String oaId) {
        return taskMapper.countByOaid(oaId) > 0;
    }

    @Override
    @Transactional
    public void uploadComplianceFile(Long taskId, String fileUrl) {
        log.info("上传合规审批文件, taskId: {}, fileUrl: {}", taskId, fileUrl);

        ExtractionTask task = taskMapper.selectById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在: " + taskId);
        }

        if (!task.isEditable()) {
            throw new RuntimeException("任务当前状态不允许上传文件");
        }

        task.setCompliancePdfUrl(fileUrl);
        taskMapper.updateById(task);

        log.info("合规审批文件上传成功, taskId: {}", taskId);
    }

    @Override
    @Transactional
    public void complianceReview(Long taskId, String fileUrl, ComplianceReviewRequest request, String reviewer) {
        log.info("合规审核, taskId: {}, fileUrl: {}, 审核意见: {}, 审核人: {}",
                taskId, fileUrl, request.getReviewOpinion(), reviewer);

        ExtractionTask task = taskMapper.selectById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在: " + taskId);
        }

        // 检查任务状态是否允许审核
        if (task.getExtractionStatusEnum() != ExtractionStatus.PENDING_COMPLIANCE_REVIEW) {
            throw new RuntimeException("任务当前状态不允许进行合规审核，当前状态: " + task.getExtractionStatusEnum().getDescription());
        }

        // 验证审核意见
        ComplianceReviewOpinion opinion;
        try {
            opinion = ComplianceReviewOpinion.valueOf(request.getReviewOpinion());
        } catch (IllegalArgumentException e) {
            throw new RuntimeException("无效的审核意见: " + request.getReviewOpinion());
        }

        // 更新任务信息
        task.setCompliancePdfUrl(fileUrl);
        task.setComplianceReviewOpinionEnum(opinion);
        task.setComplianceReviewComment(request.getReviewComment());
        task.setComplianceReviewer(reviewer);
        task.setComplianceReviewTime(LocalDateTime.now());

        // 根据审核结果更新任务状态
        if (opinion.isApproved()) {
            // 审核通过：状态改为待提数，并加入提数队列
            task.setExtractionStatusEnum(ExtractionStatus.PENDING_EXTRACTION);
            taskMapper.updateById(task);

            // 加入提数队列
            createExecutionQueueItem(taskId);

            log.info("合规审核通过，任务已加入提数队列, taskId: {}", taskId);
        } else {
            // 审核不通过：状态改为合规审核不通过
            task.setExtractionStatusEnum(ExtractionStatus.COMPLIANCE_REJECTED);
            taskMapper.updateById(task);

            log.info("合规审核不通过, taskId: {}, 原因: {}", taskId, request.getReviewComment());
        }

        log.info("合规审核完成, taskId: {}, 审核结果: {}", taskId, opinion.getDescription());
    }

    @Override
    public String generateSampleData(Long taskId) {
        log.info("生成样例数据文件, taskId: {}", taskId);
        
        ExtractionTask task = taskMapper.selectById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在: " + taskId);
        }
        
        try {
            // 获取数据源信息
            DataSource dataSource = dataSourceService.getDataSourceEntityById(task.getDataSourceId());
            String databaseType = dataSource.getType();
            
            // 执行SQL获取样例数据(前1000条)
            String sampleSql = sqlValidationService.addLimitClause(task.getExtractionScript(), 1000, databaseType);
            
            List<Map<String, Object>> sampleData = executePreviewQuery(sampleSql, dataSource);
            
            // 生成CSV文件并上传到MinIO
            String sampleFileUrl = generateAndUploadSampleFile(taskId, sampleData);
            
            // 更新任务的样例数据URL
            task.setSampleDataUrl(sampleFileUrl);
            taskMapper.updateById(task);
            
            log.info("样例数据文件生成成功, taskId: {}, fileUrl: {}", taskId, sampleFileUrl);
            return sampleFileUrl;
            
        } catch (Exception e) {
            log.error("生成样例数据文件失败, taskId: {}", taskId, e);
            throw new RuntimeException("生成样例数据文件失败: " + e.getMessage());
        }
    }

    @Override
    public boolean canSubmitTask(Long taskId) {
        ExtractionTask task = taskMapper.selectById(taskId);
        if (task == null) {
            return false;
        }
        
        ExtractionStatus status = task.getExtractionStatusEnum();
        return status == ExtractionStatus.DRAFT || status == ExtractionStatus.DATA_GENERATION_FAILED;
    }




    /**
     * 执行预览查询
     */
    private List<Map<String, Object>> executePreviewQuery(String sql, DataSource dataSource) {
        JdbcTemplate jdbcTemplate = dataSourceManager.getJdbcTemplate(dataSource);
        jdbcTemplate.setMaxRows(100);
        SqlBuilder sqlBuilder = SqlBuilderContext.getSqlBuilderService(dataSource.getType());
        String previewSql = sqlBuilder.buildPreviewSql(sql, 100);

        // todo 脱敏
        return jdbcTemplate.queryForList(previewSql);
    }

    /**
     * 生成并上传样例数据文件
     */
    private String generateAndUploadSampleFile(Long taskId, List<Map<String, Object>> sampleData) {
        // TODO: 实现CSV文件生成和MinIO上传逻辑
        return "sample_" + taskId + ".csv";
    }

    /**
     * 创建执行队列项
     */
    private void createExecutionQueueItem(Long taskId) {
        TaskExecutionQueue queueItem = new TaskExecutionQueue();
        queueItem.setTaskId(taskId);
        queueItem.setQueueStatusEnum(QueueStatus.PENDING);
        queueItem.setPriority(0);
        queueItem.setRetryCount(0);
        queueItem.setMaxRetry(3);
        queueItem.setNextExecuteTime(LocalDateTime.now());
        
        queueMapper.insert(queueItem);
        
        log.info("任务已加入执行队列, taskId: {}", taskId);
    }
}
