package com.chic.dea.domain.service.impl;

import com.chic.dea.domain.database.entity.*;
import com.chic.dea.domain.database.mapper.*;
import com.chic.dea.domain.service.*;
import com.chic.minio.MinioService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobExecution;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.*;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.model.enums.CompressionLevel;
import net.lingala.zip4j.model.enums.EncryptionMethod;

import static com.chic.dea.infrastructure.general.util.DataSourceConnectionUtil.getConnection;

/**
 * 任务执行服务实现类
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskExecutionServiceImpl implements TaskExecutionService {

    private final ExtractionTaskMapper taskMapper;
    private final TaskExecutionLogMapper logMapper;
    private final DataSourceService dataSourceService;
    //private final BigDataProcessingStrategy bigDataProcessingStrategy;
    private final EnhancedBatchJobService enhancedBatchJobService;
    private final EncryptionService encryptionService;
    private final MinioService minioService;
    
    // 正在执行的任务进度跟踪
    private final Map<Long, Integer> taskProgressMap = new ConcurrentHashMap<>();
    
    // 正在执行的任务标记
    private final Map<Long, Boolean> executingTasks = new ConcurrentHashMap<>();

    @Value("${batch.export.max-records-per-file}")
    private Integer maxRecordsPerFile;
    @Value("${batch.export.output-base-path}")
    private String outputBasePath;
    @Value("${spring.minio.bucket}")
    private String bucket;
    @Value("${spring.minio.path}")
    private String minioPath;

    @Override
    // 移除@Transactional注解，避免与Spring Batch的事务管理冲突
    // Spring Batch内部会管理自己的事务，外层事务会导致冲突
    public void executeExtractionTask(Long taskId) {
        log.info("开始执行提数任务, taskId: {}", taskId);
        
        // 标记任务为正在执行
        executingTasks.put(taskId, true);
        taskProgressMap.put(taskId, 0);
        
        LocalDateTime startTime = LocalDateTime.now();
        
        try {
            // 1. 获取任务信息
            ExtractionTask task = taskMapper.selectById(taskId);
            if (task == null) {
                throw new RuntimeException("任务不存在: " + taskId);
            }
            
            log.info("任务信息: OA ID={}, 标题={}", task.getOaId(), task.getTitle());
            
            // 2. 数据量统计步骤
            updateProgress(taskId, 10);
            recordStepLog(taskId, "DATA_COUNT", startTime, () -> {
                long totalRecords = countTotalRecords(task);
                task.setTotalRecords(totalRecords);
                taskMapper.updateById(task);
                log.info("数据量统计完成, 总记录数: {}", totalRecords);
            });
            
            // 3. 数据提取步骤
            updateProgress(taskId, 20);
            recordStepLog(taskId, "DATA_EXTRACTION", startTime, () -> {
                // 使用Spring Batch进行数据提取
                performBatchDataExtraction(task);
            });
            
            updateProgress(taskId, 80);
            
            // 4. 文件上传步骤
            recordStepLog(taskId, "FILE_UPLOAD", startTime, () -> {
                uploadResultFile(task);
                taskMapper.updateById(task);
            });
            
            updateProgress(taskId, 90);
            
            // 5. 通知发送步骤
//            recordStepLog(taskId, "NOTIFICATION", startTime, () -> {
//                sendNotification(task);
//                log.info("通知发送完成");
//            });
            
            // 6. 完成任务
            LocalDateTime endTime = LocalDateTime.now();
            taskMapper.updateExecutionResult(
                taskId, 
                endTime, 
                ExtractionStatus.DATA_PENDING_SEND.name(),
                task.getTotalRecords(),
                task.getFileSize(),
                task.getResultFileUrl(),
                task.getFilePassword()
            );
            
            updateProgress(taskId, 100);
            
            // 记录任务完成日志
            TaskExecutionLog completeLog = TaskExecutionLog.createSuccessLog(
                taskId, "TASK_COMPLETE", startTime, endTime);
            logMapper.insert(completeLog);
            
            log.info("提数任务执行完成, taskId: {}, 耗时: {}ms", 
                     taskId, java.time.Duration.between(startTime, endTime).toMillis());
            
        } catch (Exception e) {
            log.error("提数任务执行失败, taskId: {}", taskId, e);
            
            // 记录失败日志
            TaskExecutionLog failureLog = TaskExecutionLog.createFailedLog(
                taskId, "TASK_EXECUTION", startTime, e.getMessage());
            logMapper.insert(failureLog);
            
            // 更新任务状态为失败
            taskMapper.updateErrorMessage(taskId, e.getMessage());
            
            throw e;
        } finally {
            // 清理执行状态
            executingTasks.remove(taskId);
            taskProgressMap.remove(taskId);
        }
    }

    @Override
    public void cancelTask(Long taskId) {
        log.info("取消任务执行, taskId: {}", taskId);
        
        if (!isTaskExecuting(taskId)) {
            log.warn("任务未在执行中, taskId: {}", taskId);
            return;
        }
        
        // 设置取消标记
        executingTasks.put(taskId, false);
        
        // 更新任务状态
        taskMapper.updateErrorMessage(taskId, "任务被用户取消");
        
        // 记录取消日志
        TaskExecutionLog cancelLog = TaskExecutionLog.createFailedLog(
            taskId, "TASK_CANCEL", LocalDateTime.now(), "任务被用户取消");
        logMapper.insert(cancelLog);
        
        log.info("任务取消成功, taskId: {}", taskId);
    }

    @Override
    public boolean isTaskExecuting(Long taskId) {
        return Boolean.TRUE.equals(executingTasks.get(taskId));
    }

    @Override
    public Integer getTaskProgress(Long taskId) {
        return taskProgressMap.getOrDefault(taskId, 0);
    }

    /**
     * 更新任务进度
     */
    private void updateProgress(Long taskId, int progress) {
        taskProgressMap.put(taskId, progress);
        log.debug("任务 {} 进度更新: {}%", taskId, progress);
    }

    /**
     * 记录步骤执行日志
     */
    private void recordStepLog(Long taskId, String stepName, LocalDateTime taskStartTime, Runnable stepAction) {
        LocalDateTime stepStartTime = LocalDateTime.now();
        
        try {
            stepAction.run();
            
            // 记录成功日志
            TaskExecutionLog successLog = TaskExecutionLog.createSuccessLog(
                taskId, stepName, stepStartTime, LocalDateTime.now());
            logMapper.insert(successLog);
            
        } catch (Exception e) {
            // 记录失败日志
            TaskExecutionLog failureLog = TaskExecutionLog.createFailedLog(
                taskId, stepName, stepStartTime, e.getMessage());
            logMapper.insert(failureLog);
            
            throw e;
        }
    }

    /**
     * 统计总记录数
     */
    private long countTotalRecords(ExtractionTask task) {
        try {
            DataSource dataSource = dataSourceService.getDataSourceEntityById(task.getDataSourceId());
            dataSource.setPassword(encryptionService.decryptSingle(dataSource.getPassword()));
            
            // 构建COUNT查询SQL
            String countSql = buildCountSQL(task.getExtractionScript());
            
            // 执行COUNT查询
             Connection conn = getConnection(dataSource);
             PreparedStatement stmt = conn.prepareStatement(countSql);
             ResultSet rs = stmt.executeQuery();
             return rs.next() ? rs.getLong(1) : 0;
            
        } catch (Exception e) {
            log.error("统计总记录数失败", e);
            throw new RuntimeException("统计总记录数失败: " + e.getMessage());
        }
    }

    /**
     * 构建COUNT查询SQL
     */
    private String buildCountSQL(String originalSQL) {
        // 简单的COUNT查询构建
        // 实际实现中需要更复杂的SQL解析
        return "SELECT COUNT(*) FROM (" + originalSQL + ") AS count_table";
    }

    /**
     * 使用Spring Batch进行数据提取
     */
    private void performBatchDataExtraction(ExtractionTask task) {
        log.info("开始Spring Batch数据提取, taskId: {}", task.getId());

        try {
            performLargeDatasetExtraction(task);
        } catch (Exception e) {
            log.error("Spring Batch数据提取失败", e);
            throw new RuntimeException("数据提取失败: " + e.getMessage());
        }
    }

    /**
     * 大数据集处理
     */
    private void performLargeDatasetExtraction(ExtractionTask task) throws Exception {
        log.info("开始大数据集处理, taskId: {}", task.getId());

        // 使用自定义配置启动批处理作业
        JobExecution jobExecution = enhancedBatchJobService.startExtraction(task,
                task.getMaxRecordsPerFile() != null && task.getMaxRecordsPerFile()>0 ? task.getMaxRecordsPerFile() : maxRecordsPerFile, outputBasePath);

        // 监控作业执行进度
        monitorJobExecution(task, jobExecution);

        // 检查作业执行结果
        if (enhancedBatchJobService.isJobSuccessful(jobExecution)) {
            log.info("大数据集处理完成成功, taskId: {}", task.getId());
            updateTaskResultFromJobExecution(task, jobExecution);
        } else {
            String summary = enhancedBatchJobService.getJobExecutionSummary(jobExecution);
            log.error("大数据集处理失败, taskId: {}, 详情: {}", task.getId(), summary);
            throw new RuntimeException("大数据集处理作业执行失败");
        }
    }

    /**
     * 监控作业执行进度
     */
    private void monitorJobExecution(ExtractionTask task, JobExecution jobExecution) {
        log.info("开始监控作业执行, jobId: {}", jobExecution.getId());

        // 轮询检查作业状态
        while (!enhancedBatchJobService.isJobCompleted(jobExecution)) {
            try {
                Thread.sleep(2000); // 每2秒检查一次

                // 检查任务是否被取消
                if (!isTaskExecuting(task.getId())) {
                    log.warn("任务被取消，尝试停止批处理作业");
                    // TODO: 实现作业停止逻辑
                    throw new RuntimeException("任务被用户取消");
                }

                // 更新进度
                double progress = enhancedBatchJobService.getProgressPercentage(jobExecution);
                int progressInt = (int) Math.min(80, 20 + progress * 0.6); // 20%-80%区间
                updateProgress(task.getId(), progressInt);

                log.debug("作业执行进度: {:.2f}%, 任务进度: {}%", progress, progressInt);

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("作业监控被中断");
            }
        }

        log.info("作业执行完成, 最终状态: {}", jobExecution.getStatus());
    }

    /**
     * 从作业执行结果更新任务信息
     */
    private void updateTaskResultFromJobExecution(ExtractionTask task, JobExecution jobExecution) {
        // 计算处理的记录数
        long processedRecords = jobExecution.getStepExecutions().stream()
                .mapToLong(stepExecution -> stepExecution.getWriteCount())
                .sum();

        // 估算文件大小（每条记录约100字节）
        long estimatedFileSizeBytes = processedRecords * 100;
        double estimatedFileSizeMB = estimatedFileSizeBytes / (1024.0 * 1024.0);

        task.setTotalRecords(processedRecords);
        task.setFileSize(estimatedFileSizeBytes);

        log.info("任务结果更新: 处理记录数={}, 估算文件大小={}",
                processedRecords, formatFileSize(estimatedFileSizeBytes));
    }

    /**
     * 上传结果文件
     */
    private String uploadResultFile(ExtractionTask task) {
        log.info("开始上传结果文件, taskId: {}, outputPath: {}", task.getId(), task.getOutputPath());

        Path tempZipFile = null;
        try {
            // 1. 获取文件目录下的所有CSV文件
            List<Path> csvFiles = collectCsvFiles(task.getOutputPath());
            if (csvFiles.isEmpty()) {
                throw new RuntimeException("未找到任何CSV文件用于上传");
            }
            log.info("找到 {} 个CSV文件待压缩", csvFiles.size());

            // 2. 压缩打包并加密
            String filePassword = generateFilePassword();
            tempZipFile = createEncryptedZipFile(csvFiles, task.getOaId(), filePassword);
            log.info("文件压缩加密完成, 临时文件: {}", tempZipFile);

            // 3. 上传到MinIO
            String fileName = String.format("extraction_%s_%d.zip",
                                           task.getOaId(),
                                           System.currentTimeMillis());
            String fileUrl = uploadToMinio(tempZipFile, fileName);
            log.info("文件上传到MinIO完成, URL: {}", fileUrl);

            // 4. 更新任务文件信息
            updateTaskFileInfo(task, fileUrl, filePassword, tempZipFile);

            // 5. 清理原始CSV文件
            cleanupCsvFiles(csvFiles);
            log.info("原始CSV文件清理完成");

            return fileUrl;

        } catch (Exception e) {
            log.error("上传结果文件失败, taskId: {}", task.getId(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        } finally {
            // 6. 清理临时压缩文件
            if (tempZipFile != null) {
                cleanupTempFile(tempZipFile);
            }
        }
    }

    /**
     * 收集输出目录下的所有CSV文件
     */
    private List<Path> collectCsvFiles(String outputPath) throws IOException {
        if (outputPath == null || outputPath.trim().isEmpty()) {
            throw new IllegalArgumentException("输出路径不能为空");
        }

        Path outputDir = Paths.get(outputPath);
        if (!Files.exists(outputDir) || !Files.isDirectory(outputDir)) {
            throw new RuntimeException("输出目录不存在或不是目录: " + outputPath);
        }

        // 使用Files.walk来遍历目录并收集CSV文件
        try (java.util.stream.Stream<Path> pathStream = Files.walk(outputDir)) {
            List<Path> csvFiles = pathStream
                .filter(Files::isRegularFile)
                .filter(path -> path.toString().toLowerCase().endsWith(".csv"))
                .collect(Collectors.toList());

            log.info("在目录 {} 中找到 {} 个CSV文件", outputPath, csvFiles.size());
            return csvFiles;
        }
    }

    /**
     * 创建加密的ZIP文件
     */
    private Path createEncryptedZipFile(List<Path> csvFiles, String oaId, String password) throws IOException {
        // 创建临时ZIP文件
        Path tempZipFile = Files.createTempFile("extraction_" + oaId + "_", ".zip");

        try {
            // 使用zip4j库创建加密ZIP文件
            ZipFile zipFile = new ZipFile(tempZipFile.toFile(), password.toCharArray());

            // 设置加密参数
            ZipParameters zipParameters = new ZipParameters();
            zipParameters.setEncryptFiles(true);
            zipParameters.setEncryptionMethod(EncryptionMethod.ZIP_STANDARD);
            zipParameters.setCompressionLevel(CompressionLevel.NORMAL);

            // 添加所有CSV文件到ZIP
            for (Path csvFile : csvFiles) {
                log.debug("添加文件到ZIP: {}", csvFile.getFileName());
                zipFile.addFile(csvFile.toFile(), zipParameters);
            }

            log.info("创建加密ZIP文件成功: {}, 包含 {} 个文件", tempZipFile, csvFiles.size());
            return tempZipFile;

        } catch (Exception e) {
            // 如果创建失败，清理临时文件
            Files.deleteIfExists(tempZipFile);
            throw new IOException("创建加密ZIP文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 上传文件到MinIO
     */
    private String uploadToMinio(Path zipFile, String fileName) throws Exception {
        try (FileInputStream fis = new FileInputStream(zipFile.toFile())) {
            // 创建MultipartFile对象
            MultipartFile multipartFile = new MockMultipartFile(
                fileName,
                fileName,
                "application/zip",
                fis
            );

            // 调用MinIO服务上传文件
            String fileUrl = minioService.upload(bucket, multipartFile, minioPath, fileName);
            log.info("文件上传到MinIO成功: {}", fileUrl);
            return fileUrl;
        }
    }

    /**
     * 更新任务文件信息
     */
    private void updateTaskFileInfo(ExtractionTask task, String fileUrl, String password, Path zipFile) throws IOException {
        // 获取文件大小（字节）
        long fileSizeBytes = Files.size(zipFile);
        // 更新任务信息
        task.setResultFileUrl(fileUrl);
        task.setFilePassword(encryptionService.encryptSingle(password));
        task.setFileSize(fileSizeBytes); // 数据库存储字节数

        log.info("更新任务文件信息: URL={}, 文件大小={}", fileUrl, formatFileSize(fileSizeBytes));
    }

    /**
     * 清理CSV文件
     */
    private void cleanupCsvFiles(List<Path> csvFiles) {
        for (Path csvFile : csvFiles) {
            try {
                Files.deleteIfExists(csvFile);
                log.debug("删除CSV文件: {}", csvFile);
            } catch (IOException e) {
                log.warn("删除CSV文件失败: {}, 错误: {}", csvFile, e.getMessage());
            }
        }
    }

    /**
     * 清理临时文件
     */
    private void cleanupTempFile(Path tempFile) {
        try {
            Files.deleteIfExists(tempFile);
            log.debug("删除临时文件: {}", tempFile);
        } catch (IOException e) {
            log.warn("删除临时文件失败: {}, 错误: {}", tempFile, e.getMessage());
        }
    }

    /**
     * 格式化文件大小为可读格式
     */
    private String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.2f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 生成文件密码
     */
    private String generateFilePassword() {
        // 生成8位随机密码
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        StringBuilder password = new StringBuilder();

        for (int i = 0; i < 8; i++) {
            int index = (int) (Math.random() * chars.length());
            password.append(chars.charAt(index));
        }

        return password.toString();
    }

    /**
     * 发送通知
     */
    private void sendNotification(ExtractionTask task) {
        // TODO: 实现邮件和短信通知逻辑
        
        try {
            // 发送邮件通知（包含下载链接）
            sendEmailNotification(task);
            
            // 发送短信通知（包含文件密码）
            sendSmsNotification(task);
            
        } catch (Exception e) {
            log.error("发送通知失败", e);
            // 通知失败不影响任务完成
        }
    }

    /**
     * 发送邮件通知
     */
    private void sendEmailNotification(ExtractionTask task) {
        log.info("发送邮件通知给: {}", task.getApplicantEmail());
        // TODO: 实现邮件发送逻辑
    }

    /**
     * 发送短信通知
     */
    private void sendSmsNotification(ExtractionTask task) {
        log.info("发送短信通知给: {}", task.getApplicantPhone());
        // TODO: 实现短信发送逻辑
    }
}
