package com.chic.dea.domain.service.impl;

import com.chic.dea.domain.config.BatchExportConfig;
import com.chic.dea.domain.service.FileManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 文件管理器实现类
 * 支持从JobParameters动态获取输出路径
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@Component
@StepScope
public class FileManagerImpl implements FileManager {
    
    private final BatchExportConfig config;
    private final AtomicInteger currentFileIndex = new AtomicInteger(1);
    private final AtomicInteger currentFileRecords = new AtomicInteger(0);
    private volatile String currentFilePath;
    private volatile String baseTimestamp;

    /**
     * 从JobParameters中注入输出路径，如果没有则为null
     */
    @Value("#{jobParameters['outputPath'] ?: null}")
    private String jobOutputPath;

    public FileManagerImpl(BatchExportConfig config) {
        this.config = config;
        this.baseTimestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        // 注意：不在构造函数中初始化currentFilePath，因为jobOutputPath还未注入
        // 将在getCurrentFilePath()中进行延迟初始化
    }
    
    @Override
    public String getCurrentFilePath() {
        // 延迟初始化，确保jobOutputPath已经注入
        if (currentFilePath == null) {
            currentFilePath = generateFilePath(currentFileIndex.get());
            // 确保目录存在
            ensureDirectoryExists(currentFilePath);
            log.info("初始化文件路径：{}", currentFilePath);
        }
        return currentFilePath;
    }
    
    @Override
    public boolean shouldSwitchFile(int currentRecords) {
        return currentRecords >= config.getMaxRecordsPerFile();
    }
    
    @Override
    public String switchToNewFile() {
        int newIndex = currentFileIndex.incrementAndGet();
        currentFileRecords.set(0);
        currentFilePath = generateFilePath(newIndex);
        
        // 确保目录存在
        ensureDirectoryExists(currentFilePath);
        
        log.info("切换到新文件：{}", currentFilePath);
        return currentFilePath;
    }
    
    @Override
    public int getCurrentFileRecords() {
        return currentFileRecords.get();
    }
    
    @Override
    public void incrementRecords(int count) {
        currentFileRecords.addAndGet(count);
    }
    
    @Override
    public void reset() {
        currentFileIndex.set(1);
        currentFileRecords.set(0);
        baseTimestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        // 重置时清空currentFilePath，让它在下次调用getCurrentFilePath()时重新初始化
        currentFilePath = null;

        log.info("文件管理器已重置，将在下次获取路径时重新初始化");
    }
    
    @Override
    public int getGeneratedFileCount() {
        return currentFileIndex.get();
    }
    
    /**
     * 生成文件路径
     * 优先使用JobParameters中的outputPath，如果没有则使用配置文件中的默认路径
     */
    private String generateFilePath(int fileIndex) {
        // 优先使用JobParameters中的outputPath
        String basePath = (jobOutputPath != null && !jobOutputPath.trim().isEmpty())
            ? jobOutputPath
            : config.getOutputBasePath();

        log.debug("使用输出路径: {} (来源: {})", basePath,
                 (jobOutputPath != null && !jobOutputPath.trim().isEmpty()) ? "JobParameters" : "配置文件");

        return String.format("%s/%s_%s_part%03d.csv",
                basePath,
                config.getFilePrefix(),
                baseTimestamp,
                fileIndex);
    }
    
    /**
     * 确保目录存在
     */
    private void ensureDirectoryExists(String filePath) {
        File file = new File(filePath);
        File parentDir = file.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            boolean created = parentDir.mkdirs();
            if (created) {
                log.info("创建输出目录：{}", parentDir.getAbsolutePath());
            } else {
                log.warn("创建输出目录失败：{}", parentDir.getAbsolutePath());
            }
        }
    }
}
