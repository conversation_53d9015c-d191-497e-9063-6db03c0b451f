package com.chic.dea.domain.batch;

import com.chic.dea.domain.service.FileManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 文件统计监听器
 * 监听批处理作业执行过程并统计文件信息
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@Component
public class FileStatisticsListener implements JobExecutionListener, StepExecutionListener {
    
    @Autowired
    private FileManager fileManager;
    
    @Autowired
    private EnhancedDynamicCsvWriter enhancedDynamicCsvWriter;
    
    private final Map<String, Integer> fileRecordCounts = new ConcurrentHashMap<>();
    private long jobStartTime;
    private long stepStartTime;

    // 在Step结束时收集的统计信息，避免在Job级别访问@StepScope的Bean
    private int finalGeneratedFileCount = 0;
    private String finalCurrentFilePath = "";
    private int finalCurrentFileRecords = 0;
    
    @Override
    public void beforeJob(JobExecution jobExecution) {
        jobStartTime = System.currentTimeMillis();
        log.info("=== 数据提取作业开始 ===");
        log.info("作业ID: {}", jobExecution.getId());
        log.info("作业名称: {}", jobExecution.getJobInstance().getJobName());

        JobParameters parameters = jobExecution.getJobParameters();
        log.info("作业参数:");
        parameters.getParameters().forEach((key, value) ->
            log.info("  {}: {}", key, value.getValue()));

        // 注意：不在这里重置@StepScope的Bean，因为Step上下文还未激活
        // 重置操作移到beforeStep()方法中
    }
    
    @Override
    public void afterJob(JobExecution jobExecution) {
        long jobEndTime = System.currentTimeMillis();
        long totalTime = jobEndTime - jobStartTime;
        
        log.info("=== 数据提取作业完成 ===");
        log.info("作业状态: {}", jobExecution.getStatus());
        log.info("总处理时间: {} ms ({} 秒)", totalTime, totalTime / 1000.0);
        
        // 刷新所有缓冲区
        try {
            enhancedDynamicCsvWriter.flushAllBuffers();
        } catch (Exception e) {
            log.error("刷新缓冲区失败", e);
        }
        
        // 输出文件统计信息
        printFileStatistics(jobExecution);
        
        // 输出性能统计
        printPerformanceStatistics(jobExecution, totalTime);
    }
    
    @Override
    public void beforeStep(StepExecution stepExecution) {
        stepStartTime = System.currentTimeMillis();
        log.info("=== Step开始执行 ===");
        log.info("Step名称: {}", stepExecution.getStepName());

        // 在Step开始时重置文件管理器和写入器状态
        // 此时Step上下文已激活，可以安全访问@StepScope的Bean
        try {
            fileManager.reset();
            enhancedDynamicCsvWriter.reset();
            log.info("文件管理器和写入器状态已重置");
        } catch (Exception e) {
            log.warn("重置文件管理器状态时出现异常: {}", e.getMessage());
        }
    }
    
    @Override
    public ExitStatus afterStep(StepExecution stepExecution) {
        long stepEndTime = System.currentTimeMillis();
        long stepTime = stepEndTime - stepStartTime;
        
        log.info("=== Step执行完成 ===");
        log.info("Step名称: {}", stepExecution.getStepName());
        log.info("Step状态: {}", stepExecution.getStatus());
        log.info("Step执行时间: {} ms ({} 秒)", stepTime, stepTime / 1000.0);
        log.info("Step统计:");
        log.info("- 读取记录数: {}", stepExecution.getReadCount());
        log.info("- 写入记录数: {}", stepExecution.getWriteCount());
        log.info("- 跳过记录数: {}", stepExecution.getSkipCount());
        log.info("- 提交次数: {}", stepExecution.getCommitCount());
        log.info("- 回滚次数: {}", stepExecution.getRollbackCount());
        
        // 计算处理速度
        if (stepTime > 0) {
            double recordsPerSecond = (double) stepExecution.getWriteCount() * 1000 / stepTime;
            log.info("- 处理速度: {:.2f} 记录/秒", recordsPerSecond);
        }

        // 在Step结束时收集文件统计信息，避免在Job级别访问@StepScope的Bean
        try {
            finalGeneratedFileCount = fileManager.getGeneratedFileCount();
            finalCurrentFilePath = fileManager.getCurrentFilePath();
            finalCurrentFileRecords = fileManager.getCurrentFileRecords();
            log.info("收集文件统计信息完成");
        } catch (Exception e) {
            log.warn("收集文件统计信息时出现异常: {}", e.getMessage());
        }

        return null;
    }
    
    /**
     * 输出文件统计信息
     * 使用在Step结束时收集的信息，避免访问@StepScope的Bean
     */
    private void printFileStatistics(JobExecution jobExecution) {
        log.info("=== 生成文件统计 ===");

        log.info("生成文件数量: {}", finalGeneratedFileCount);

        // 计算总记录数
        long totalRecords = jobExecution.getStepExecutions().stream()
                .mapToLong(StepExecution::getWriteCount)
                .sum();

        log.info("总处理记录数: {}", totalRecords);

        if (finalGeneratedFileCount > 0) {
            long avgRecordsPerFile = totalRecords / finalGeneratedFileCount;
            log.info("平均每文件记录数: {}", avgRecordsPerFile);
        }

        // 输出当前文件信息（使用收集到的信息）
        if (!finalCurrentFilePath.isEmpty()) {
            log.info("最终文件路径: {}", finalCurrentFilePath);
            log.info("最终文件记录数: {}", finalCurrentFileRecords);
        }
    }
    
    /**
     * 输出性能统计信息
     */
    private void printPerformanceStatistics(JobExecution jobExecution, long totalTime) {
        log.info("=== 性能统计 ===");
        
        long totalRecords = jobExecution.getStepExecutions().stream()
                .mapToLong(StepExecution::getWriteCount)
                .sum();
        
        if (totalTime > 0 && totalRecords > 0) {
            double recordsPerSecond = (double) totalRecords * 1000 / totalTime;
            double recordsPerMinute = recordsPerSecond * 60;
            
            log.info("总体处理速度:");
            log.info("- {:.2f} 记录/秒", recordsPerSecond);
            log.info("- {:.2f} 记录/分钟", recordsPerMinute);
            
            // 估算文件大小（假设每条记录平均100字节）
            long estimatedFileSize = totalRecords * 100;
            double fileSizeMB = estimatedFileSize / (1024.0 * 1024.0);
            log.info("- 估算文件大小: {:.2f} MB", fileSizeMB);
            
            if (totalTime > 0) {
                double mbPerSecond = fileSizeMB * 1000 / totalTime;
                log.info("- 文件生成速度: {:.2f} MB/秒", mbPerSecond);
            }
        }
        
        // 内存使用情况
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        log.info("内存使用情况:");
        log.info("- 已用内存: {:.2f} MB", usedMemory / (1024.0 * 1024.0));
        log.info("- 总内存: {:.2f} MB", totalMemory / (1024.0 * 1024.0));
        log.info("- 内存使用率: {:.2f}%", (double) usedMemory / totalMemory * 100);
    }
    
    /**
     * 记录文件统计信息
     */
    public void recordFileStatistics(String filePath, int recordCount) {
        fileRecordCounts.put(filePath, recordCount);
    }
    
    /**
     * 获取文件统计信息
     */
    public Map<String, Integer> getFileStatistics() {
        return new ConcurrentHashMap<>(fileRecordCounts);
    }
}
