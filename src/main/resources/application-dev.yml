server:
  port: 8248
  servlet:
    context-path: /data-extract-audit
    multipart:
      enabled: true
      file-size-threshold: 2KB
      location: ./tmp
      max-file-size: 10MB
      max-request-size: 10MB
  # 配置tomcat
  tomcat:
    # 最大连接数 默认10000
    max-connections: 10000
    # 最大并发数 默认200
    max-threads: 2000
    accept-count: 500
    # 超时时长
    connection-timeout: 15000

spring:
  minio:
    enable: true
    url: http://lemon-oss-inc.chinahuanong.com.cn
    access-key: 8NWUpPHQQzKLt49E
    secret-key: pBBaJw8PtTmYNKsFJ2YIMiETtfNsQ7E1
    bucket: ticm-test
    path: /data-export/
    compliance-path: compliance
  http:
    multipart:
      location: ./tmp
  ##数据库连接信息
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    #动态数据源配置
    dynamic:
      druid: #以下是全局默认值，可以全局更改
        initial-size: 1
        min-idle: 1
        maxActive: 20
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连 接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true  #是否对已备语句进行池管理（布尔值），是否对PreparedStatement进行缓存
        # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙，此处是filter修改的地方
        filters:
          commons-log.connection-logger-name: stat
        # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
        # connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
        # 合并多个DruidDataSource的监控数据Z
        useGlobalDataSourceStat: true
      #指定主数据源 默认为master
      primary: data-extract-audit
      # true：找不到数据源报错，false：找不到数据源则使用数据源
      strict: false
      datasource:
        #核心库
        data-extract-audit:
          url: **************************************************************************************************************************
          username: extractaudit
          password: fdjsi##j121
          driver-class-name: com.mysql.cj.jdbc.Driver
          druid:
            validationQuery: select 1
        # oa 为sqlserver
        oa:
          url: *****************************************************************************************************************************************************************
          username: sa
          password: china@123
          driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
          druid:
            validationQuery: select 1
            # 增加连接池配置，提高容错性
            initial-size: 0
            min-idle: 0
            max-active: 5
            max-wait: 60000
            test-while-idle: true
            test-on-borrow: true
            test-on-return: false
            time-between-eviction-runs-millis: 60000
            min-evictable-idle-time-millis: 300000
            validation-query-timeout: 30
        tianyu:
          url: ********************************************
          username: data_classification
          password: agne###123A
          driver-class-name: oracle.jdbc.driver.OracleDriver
          druid:
            validationQuery: select 1 FROM dual
#            initial-size: 0
#            min-idle: 0
#            max-active: 5
#            max-wait: 30000
#            validation-query: select 1
#            test-while-idle: true
#            test-on-borrow: true
#            test-on-return: false
#            time-between-eviction-runs-millis: 60000
#            min-evictable-idle-time-millis: 300000
#            validation-query-timeout: 30

  redis:
    #redis数据库索引(默认为0)
    database: 9
    #redis服务器地址
    host: **********
    #redis服务器连接端口
    port: 6379
    #redis连接密码
#    password: porsche
    redisson:
      pool:
        size: 5
      #redis连接池设置
      # Redis Cluster集群节点配置
      #    cluster:
      #        # Redis 集群地址信息
      #      nodes:
      #        - ***************:7001
      #        - ***************:7002
      #        - ***************:7003
      #        - ***************:7004
      #        - ***************:7005
      #        - ***************:7006
      # 获取失败 最大重定向次数
    #      max-redirects: 3
    #如果用以前的jedis，可以把下面的lettuce换成jedis即可
    lettuce:
      pool:
        # 连接池最大连接数默认值为8
        max-active: 1000
        # 连接池最大阻塞时间（使用负值表示没有限制）默认值为-1
        max-wait: -1
        # 连接池中最大空闲连接数默认值为8
        max-idle: 10
        # 连接池中的最小空闲连接数，默认值为0
        min-idle: 10
        size: 5
        connectionPoolSize: 5
        connectionMinimumIdleSize: 1

  jackson:
    date-format: yyyy-MM-dd
    time-zone: Asia/Shanghai
logging:
  config: classpath:logback-spring.xml
  level:
    root: INFO
    com.chic: info

#mybatis
mybatis-plus:
  #mapper配置文件
  mapper-locations: classpath:/mapper/**/*.xml
  type-aliases-package: com.chic.dea.domain.**.entity
  #全局配置
  global-config:
    # 主键类型  0:"数据库ID自增", 1:"用户输入ID",2:"全局唯一ID (数字类型唯一ID)", 3:"全局唯一ID UUID";
    id-type: 0
    # 刷新mapper 调试神器
    refresh-mapper: true
    # 字段策略 0:"忽略判断",1:"非 NULL 判断",2:"非空判断"
    field-strategy: 2
  #开启驼峰命名
  configuration:
    map-underscore-to-camel-case: false
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    jdbc-type-for-null: 'null'
    default-statement-timeout: 25

#mybatis分页插件
pagehelper:
  helperDialect: oracle
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql
  row-bounds-with-count: true
  offset-as-page-num: true


## 自定义配置
custom:
  # 数据库key
  database-key: xTvJb8wiFB08fFs37Jiw
  # 天权地址
  skyauth-url: http://porsche-ca.chinahuanong.com.cn/skyauth
  appId: data-quality
  ## 消息平台调用
  sendMsg:
    msgUrl: "https://hermes-tools.chinahuanong.com.cn/msg/sms/sendSmsNotice?appid=100001&appkey=123123"
    appId: 100001
    appKey: 123123
  ## OA表名配置
  oa:
    # OA主表名
    main-table: formtable_main_69
  ## SQL血缘服务配置
  sql-lineage:
    # 血缘服务地址
    url: http://127.0.0.1:8888/api/v1/lineage/column

