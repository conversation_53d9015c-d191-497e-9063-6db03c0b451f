package com.chic.dea.domain.service.impl;

import com.chic.dea.domain.config.BatchExportConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.test.util.ReflectionTestUtils;

import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * FileManagerImpl 单元测试
 * 测试JobParameters动态路径功能
 */
@ExtendWith(MockitoExtension.class)
class FileManagerImplTest {

    @Mock
    private BatchExportConfig config;

    private FileManagerImpl fileManager;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        // 设置配置默认值
        when(config.getOutputBasePath()).thenReturn("/default/path");
        when(config.getFilePrefix()).thenReturn("test_export");
        when(config.getMaxRecordsPerFile()).thenReturn(1000L);
        when(config.getBufferFlushThreshold()).thenReturn(100);
        
        fileManager = new FileManagerImpl(config);
    }

    @Test
    void testGetCurrentFilePath_WithoutJobParameters() {
        // 测试没有JobParameters时使用默认配置路径
        String filePath = fileManager.getCurrentFilePath();
        
        assertNotNull(filePath);
        assertTrue(filePath.startsWith("/default/path"));
        assertTrue(filePath.contains("test_export"));
        assertTrue(filePath.endsWith("_part001.csv"));
    }

    @Test
    void testGetCurrentFilePath_WithJobParameters() {
        // 模拟JobParameters中的outputPath
        String jobOutputPath = tempDir.toString() + "/dynamic/path";
        ReflectionTestUtils.setField(fileManager, "jobOutputPath", jobOutputPath);
        
        String filePath = fileManager.getCurrentFilePath();
        
        assertNotNull(filePath);
        assertTrue(filePath.startsWith(jobOutputPath));
        assertTrue(filePath.contains("test_export"));
        assertTrue(filePath.endsWith("_part001.csv"));
    }

    @Test
    void testGetCurrentFilePath_WithEmptyJobParameters() {
        // 测试JobParameters为空字符串时回退到默认配置
        ReflectionTestUtils.setField(fileManager, "jobOutputPath", "");
        
        String filePath = fileManager.getCurrentFilePath();
        
        assertNotNull(filePath);
        assertTrue(filePath.startsWith("/default/path"));
        assertTrue(filePath.contains("test_export"));
    }

    @Test
    void testSwitchToNewFile_WithJobParameters() {
        // 设置JobParameters路径
        String jobOutputPath = tempDir.toString() + "/dynamic/path";
        ReflectionTestUtils.setField(fileManager, "jobOutputPath", jobOutputPath);
        
        // 获取初始文件路径
        String firstFile = fileManager.getCurrentFilePath();
        assertTrue(firstFile.endsWith("_part001.csv"));
        
        // 切换到新文件
        String secondFile = fileManager.switchToNewFile();
        
        assertNotNull(secondFile);
        assertTrue(secondFile.startsWith(jobOutputPath));
        assertTrue(secondFile.endsWith("_part002.csv"));
        assertNotEquals(firstFile, secondFile);
    }

    @Test
    void testReset_ClearsCurrentFilePath() {
        // 先获取一个文件路径
        String initialPath = fileManager.getCurrentFilePath();
        assertNotNull(initialPath);
        
        // 重置
        fileManager.reset();
        
        // 验证内部状态被重置
        assertEquals(1, fileManager.getGeneratedFileCount());
        assertEquals(0, fileManager.getCurrentFileRecords());
        
        // 验证路径会重新生成（时间戳会不同）
        String newPath = fileManager.getCurrentFilePath();
        assertNotNull(newPath);
        // 由于时间戳不同，路径应该不同
        // 但这个测试可能在同一秒内执行，所以我们只验证基本结构
        assertTrue(newPath.endsWith("_part001.csv"));
    }

    @Test
    void testShouldSwitchFile() {
        // 测试文件切换逻辑
        assertFalse(fileManager.shouldSwitchFile(500));
        assertTrue(fileManager.shouldSwitchFile(1000));
        assertTrue(fileManager.shouldSwitchFile(1500));
    }

    @Test
    void testIncrementRecords() {
        assertEquals(0, fileManager.getCurrentFileRecords());
        
        fileManager.incrementRecords(10);
        assertEquals(10, fileManager.getCurrentFileRecords());
        
        fileManager.incrementRecords(5);
        assertEquals(15, fileManager.getCurrentFileRecords());
    }
}
