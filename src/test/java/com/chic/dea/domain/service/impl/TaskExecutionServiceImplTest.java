package com.chic.dea.domain.service.impl;

import com.chic.dea.domain.database.entity.ExtractionTask;
import com.chic.dea.domain.database.mapper.ExtractionTaskMapper;
import com.chic.dea.domain.database.mapper.TaskExecutionLogMapper;
import com.chic.dea.domain.service.DataSourceService;
import com.chic.dea.domain.service.EnhancedBatchJobService;
import com.chic.dea.domain.service.EncryptionService;
import com.chic.minio.MinioService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * TaskExecutionServiceImpl 单元测试
 */
@ExtendWith(MockitoExtension.class)
class TaskExecutionServiceImplTest {

    @Mock
    private ExtractionTaskMapper taskMapper;
    
    @Mock
    private TaskExecutionLogMapper logMapper;
    
    @Mock
    private DataSourceService dataSourceService;
    
    @Mock
    private EnhancedBatchJobService enhancedBatchJobService;
    
    @Mock
    private EncryptionService encryptionService;
    
    @Mock
    private MinioService minioService;

    @InjectMocks
    private TaskExecutionServiceImpl taskExecutionService;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        // 设置配置值
        ReflectionTestUtils.setField(taskExecutionService, "bucket", "test-bucket");
        ReflectionTestUtils.setField(taskExecutionService, "minioPath", "/test-path/");
        ReflectionTestUtils.setField(taskExecutionService, "outputBasePath", tempDir.toString());
    }

    @Test
    void testUploadResultFile_Success() throws Exception {
        // 准备测试数据
        ExtractionTask task = new ExtractionTask();
        task.setId(1L);
        task.setOaId("TEST001");
        
        // 创建测试CSV文件
        Path csvDir = tempDir.resolve("csv-output");
        Files.createDirectories(csvDir);
        task.setOutputPath(csvDir.toString());
        
        Path csvFile1 = csvDir.resolve("data1.csv");
        Path csvFile2 = csvDir.resolve("data2.csv");
        Files.write(csvFile1, "id,name\n1,test1\n2,test2".getBytes());
        Files.write(csvFile2, "id,name\n3,test3\n4,test4".getBytes());

        // Mock MinIO服务
        when(minioService.upload(eq("test-bucket"), any(MultipartFile.class), eq("/test-path/"), anyString()))
            .thenReturn("http://minio-server:9000/test-bucket/test-path/extraction_TEST001_123456.zip");

        // 使用反射调用私有方法
        String result = (String) ReflectionTestUtils.invokeMethod(taskExecutionService, "uploadResultFile", task);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains("extraction_TEST001_"));
        assertTrue(result.endsWith(".zip"));
        
        // 验证MinIO上传被调用
        verify(minioService, times(1)).upload(eq("test-bucket"), any(MultipartFile.class), eq("/test-path/"), anyString());
        
        // 验证任务信息被更新
        assertNotNull(task.getResultFileUrl());
        assertNotNull(task.getFilePassword());
        assertTrue(task.getFileSize() > 0);
        
        // 验证CSV文件被清理
        assertFalse(Files.exists(csvFile1));
        assertFalse(Files.exists(csvFile2));
    }

    @Test
    void testUploadResultFile_NoCsvFiles() {
        // 准备测试数据
        ExtractionTask task = new ExtractionTask();
        task.setId(1L);
        task.setOaId("TEST002");
        
        // 创建空目录
        Path emptyDir = tempDir.resolve("empty-output");
        try {
            Files.createDirectories(emptyDir);
            task.setOutputPath(emptyDir.toString());
        } catch (IOException e) {
            fail("Failed to create test directory");
        }

        // 调用方法并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            ReflectionTestUtils.invokeMethod(taskExecutionService, "uploadResultFile", task);
        });
        
        assertTrue(exception.getMessage().contains("未找到任何CSV文件用于上传"));
    }

    @Test
    void testUploadResultFile_InvalidOutputPath() {
        // 准备测试数据
        ExtractionTask task = new ExtractionTask();
        task.setId(1L);
        task.setOaId("TEST003");
        task.setOutputPath("/non-existent-path");

        // 调用方法并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            ReflectionTestUtils.invokeMethod(taskExecutionService, "uploadResultFile", task);
        });
        
        assertTrue(exception.getMessage().contains("文件上传失败"));
    }

    @Test
    void testCollectCsvFiles_Success() throws Exception {
        // 创建测试CSV文件
        Path csvDir = tempDir.resolve("csv-test");
        Files.createDirectories(csvDir);
        
        Path csvFile1 = csvDir.resolve("test1.csv");
        Path csvFile2 = csvDir.resolve("test2.CSV");
        Path txtFile = csvDir.resolve("test.txt");
        
        Files.write(csvFile1, "test data 1".getBytes());
        Files.write(csvFile2, "test data 2".getBytes());
        Files.write(txtFile, "not csv".getBytes());

        // 调用私有方法
        @SuppressWarnings("unchecked")
        java.util.List<Path> result = (java.util.List<Path>) ReflectionTestUtils.invokeMethod(
            taskExecutionService, "collectCsvFiles", csvDir.toString());

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.stream().anyMatch(p -> p.getFileName().toString().equals("test1.csv")));
        assertTrue(result.stream().anyMatch(p -> p.getFileName().toString().equals("test2.CSV")));
    }

    @Test
    void testGenerateFilePassword() {
        // 调用私有方法
        String password = (String) ReflectionTestUtils.invokeMethod(taskExecutionService, "generateFilePassword");

        // 验证结果
        assertNotNull(password);
        assertEquals(8, password.length());
        assertTrue(password.matches("[A-Z0-9]+"));
    }

    @Test
    void testFormatFileSize() {
        // 测试不同大小的文件格式化
        assertEquals("512 B", ReflectionTestUtils.invokeMethod(taskExecutionService, "formatFileSize", 512L));
        assertEquals("1.50 KB", ReflectionTestUtils.invokeMethod(taskExecutionService, "formatFileSize", 1536L));
        assertEquals("2.50 MB", ReflectionTestUtils.invokeMethod(taskExecutionService, "formatFileSize", 2621440L));
        assertEquals("1.00 GB", ReflectionTestUtils.invokeMethod(taskExecutionService, "formatFileSize", 1073741824L));
    }
}
